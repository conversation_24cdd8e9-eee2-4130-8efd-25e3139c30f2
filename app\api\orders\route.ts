import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'
import { z } from 'zod'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Order creation schema
const createOrderSchema = z.object({
  productId: z.string().uuid('Invalid product ID'),
  packageId: z.string().uuid('Invalid package ID'),
  quantity: z.number().int().min(1).max(100),
  customData: z.record(z.any()).optional(),
  paymentMethod: z.enum(['wallet', 'external']).default('wallet'),
  currencyCode: z.string().length(3, 'Currency code must be 3 characters')
})

// GET /api/orders - Get user's orders
export async function GET(request: NextRequest) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 50)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()

    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        error: 'Unauthorized'
      }, { status: 401 })
    }

    // Get user's tenant
    console.log('Orders API - Fetching user profile for user ID:', user.id)
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('tenant_id, name, email')
      .eq('id', user.id)
      .single()

    console.log('Orders API - Profile:', profile, 'Error:', profileError)

    if (!profile) {
      console.log('Orders API - No user profile found')
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Get user's orders with product and package details

    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        id,
        amount,
        status,
        custom_data,
        created_at,
        updated_at,
        products (
          id,
          title,
          slug,
          cover_image
        ),
        packages (
          id,
          name,
          price,
          image
        )
      `)
      .eq('user_id', user.id)
      .eq('tenant_id', profile.tenant_id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Orders API - Database error:', error)
      return NextResponse.json({ error: 'Failed to fetch orders', details: error.message }, { status: 500 })
    }

    console.log('Orders API - Found orders:', orders?.length || 0)

    return NextResponse.json({
      success: true,
      orders: orders || []
    })

  } catch (error) {
    console.error('Error in GET /api/orders:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST /api/orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { productId, packageId, quantity, customData, paymentMethod, currencyCode } = createOrderSchema.parse(body)

    // Get user's tenant and profile
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id, name, email')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Verify product exists and belongs to tenant
    const { data: product } = await supabase
      .from('products')
      .select('id, title, slug')
      .eq('id', productId)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    // Verify package exists and belongs to product
    const { data: packageData } = await supabase
      .from('packages')
      .select('id, name, price')
      .eq('id', packageId)
      .eq('product_id', productId)
      .single()

    if (!packageData) {
      return NextResponse.json({ error: 'Package not found' }, { status: 404 })
    }

    // Calculate total amount in USD (packages are stored in USD)
    const totalAmountUSD = packageData.price * quantity

    // Get currency exchange rate for conversion
    const { data: currency } = await supabase
      .from('currencies')
      .select('exchange_rate')
      .eq('code', currencyCode)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!currency) {
      return NextResponse.json({ error: 'Currency not supported' }, { status: 400 })
    }

    // Convert to user's selected currency
    const totalAmountInCurrency = totalAmountUSD * currency.exchange_rate

    // If paying with wallet, check and deduct balance
    if (paymentMethod === 'wallet') {
      // Get current balance
      const { data: balanceData } = await supabase
        .from('user_currency_balances')
        .select('balance')
        .eq('user_id', user.id)
        .eq('currency_code', currencyCode)
        .eq('tenant_id', profile.tenant_id)
        .single()

      const currentBalance = balanceData?.balance || 0

      if (currentBalance < totalAmountInCurrency) {
        return NextResponse.json({ 
          error: 'Insufficient balance',
          required: totalAmountInCurrency,
          available: currentBalance,
          currency: currencyCode
        }, { status: 400 })
      }

      // Deduct balance
      const newBalance = currentBalance - totalAmountInCurrency
      const { error: balanceError } = await supabase
        .from('user_currency_balances')
        .update({ 
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .eq('currency_code', currencyCode)
        .eq('tenant_id', profile.tenant_id)

      if (balanceError) {
        console.error('Error updating balance:', balanceError)
        return NextResponse.json({ error: 'Failed to process payment' }, { status: 500 })
      }

      // Log balance change
      await supabase
        .from('balance_change_log')
        .insert({
          user_id: user.id,
          currency_code: currencyCode,
          amount_change: -totalAmountInCurrency,
          balance_before: currentBalance,
          balance_after: newBalance,
          change_type: 'purchase',
          notes: `Purchase: ${product.title} - ${packageData.name} x${quantity}`,
          tenant_id: profile.tenant_id
        })
    }

    // Create order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        user_id: user.id,
        product_id: productId,
        package_id: packageId,
        amount: totalAmountUSD, // Store in USD for consistency
        status: paymentMethod === 'wallet' ? 'completed' : 'pending',
        custom_data: {
          ...customData,
          quantity,
          currency_code: currencyCode,
          amount_in_currency: totalAmountInCurrency,
          payment_method: paymentMethod
        },
        tenant_id: profile.tenant_id
      })
      .select()
      .single()

    if (orderError) {
      return NextResponse.json({ error: 'Failed to create order' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      order: {
        id: order.id,
        status: order.status,
        amount: totalAmountInCurrency,
        currency: currencyCode,
        product: product.title,
        package: packageData.name,
        quantity,
        createdAt: order.created_at
      }
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }
    
    console.error('Error in POST /api/orders:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
