"use client"

import { useState, useEffect, use } from "react"
import { useRouter } from "next/navigation"
import { notFound } from "next/navigation"
import ProductDetail from "../../components/ProductDetail"
import PackageSelector from "../../components/PackageSelector"
import PurchaseModal from "../../components/PurchaseModal"
import { useData } from "../../contexts/DataContext"
import type { Product, Package } from "../../types"

interface ProductPageProps {
  params: Promise<{
    slug: string
  }>
}

export default function ProductPage({ params }: ProductPageProps) {
  const router = useRouter()
  // Unwrap the params Promise using React.use()
  const { slug } = use(params)

  // Use centralized data context
  const { products } = useData()

  const [product, setProduct] = useState<Product | null>(null)
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null)
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [purchaseQuantity, setPurchaseQuantity] = useState(1)

  useEffect(() => {
    // Find product by slug
    const foundProduct = products.find(p => p.slug === slug)
    if (foundProduct) {
      setProduct(foundProduct)
    } else {
      notFound()
    }
  }, [slug, products])

  const handleBack = () => {
    router.back()
  }

  const handlePackageSelect = (pkg: Package) => {
    setSelectedPackage(pkg)
  }

  const handleTopUp = (quantity: number) => {
    setPurchaseQuantity(quantity)
    setShowPurchaseModal(true)
  }

  const handlePurchase = async (formData: any) => {
    try {
      console.log("Purchase completed:", {
        product: product?.id,
        package: selectedPackage?.id,
        quantity: purchaseQuantity,
        formData
      })

      // Close modal
      setShowPurchaseModal(false)

      // Redirect to orders page to see the new order
      router.push("/orders")
    } catch (error) {
      console.error("Error handling purchase:", error)
    }
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p>جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <ProductDetail
        product={product}
        onBack={handleBack}
        onPackageSelect={handlePackageSelect}
        selectedPackage={selectedPackage}
      />

      <PackageSelector
        selectedPackage={selectedPackage}
        onTopUp={handleTopUp}
      />

      <PurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        product={product}
        selectedPackage={selectedPackage}
        quantity={purchaseQuantity}
        onPurchase={handlePurchase}
      />
    </>
  )
}


